#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港食物安全中心网站抓取脚本
抓取https://www.cfs.gov.hk/sc_chi/whatsnew/whatsnew_fstr/whatsnew_fstr.html页面的内容
"""

import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import urllib3
from datetime import datetime, timedelta

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class CFSHKScraper:
    def __init__(self):
        self.base_url = "https://www.cfs.gov.hk"
        self.target_url = "https://www.cfs.gov.hk/sc_chi/whatsnew/whatsnew_fstr/whatsnew_fstr.html"
        self.session = requests.Session()

        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def fetch_page(self, url):
        """获取网页内容"""
        try:
            # 禁用SSL证书验证来解决证书问题
            response = self.session.get(url, timeout=30, verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def convert_date_format(self, date_str):
        """将日期从 '21.7.2025' 格式转换为 '2025-07-21' 格式"""
        try:
            # 解析原始日期格式 dd.mm.yyyy
            date_obj = datetime.strptime(date_str, '%d.%m.%Y')
            # 转换为目标格式 yyyy-mm-dd
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            # 如果解析失败，返回原始字符串
            return date_str

    def is_within_last_month(self, date_str):
        """检查日期是否在最近一个月内"""
        try:
            # 解析日期
            date_obj = datetime.strptime(date_str, '%d.%m.%Y')
            # 获取当前日期
            current_date = datetime.now()
            # 计算一个月前的日期
            one_month_ago = current_date - timedelta(days=30)
            # 检查是否在范围内
            return date_obj >= one_month_ago and date_obj <= current_date
        except ValueError:
            # 如果解析失败，默认保留
            return True

    def parse_news_content(self, html_content):
        """解析新闻内容"""
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []

        # 查找所有tr元素
        table_rows = soup.find_all('tr')

        for tr in table_rows:
            # 查找包含日期的td.subHeader
            date_td = tr.find('td', class_='subHeader')
            if date_td:
                # 提取日期文本，去掉箭头符号
                date_text = date_td.get_text().strip()
                # 移除箭头符号和其他特殊字符
                date_text = date_text.replace('▼', '').replace('▲', '').replace('►', '').replace('◄', '').replace('&nbsp;', '').strip()

                # 检查日期是否在最近一个月内
                if not self.is_within_last_month(date_text):
                    continue

                # 转换日期格式
                formatted_date = self.convert_date_format(date_text)

                # 查找同一行中的其他td，包含ul和li
                content_td = tr.find('td', class_=lambda x: x != 'subHeader')
                if content_td:
                    # 查找所有li元素
                    li_elements = content_td.find_all('li')

                    if li_elements:
                        # 为每个li创建一个独立的新闻项目
                        for li in li_elements:
                            # 获取li的文本内容
                            li_text = li.get_text().strip()
                            # 如果li中有链接，也提取链接信息
                            link = li.find('a')
                            if link:
                                href = link.get('href', '')
                                if href:
                                    full_url = urljoin(self.base_url, href)
                                    li_text += f" [链接: {full_url}]"

                            # 为每个li创建一个独立的新闻项目
                            news_item = {
                                '日期': formatted_date,
                                '内容': li_text
                            }
                            news_items.append(news_item)

        return news_items

    def scrape_news(self):
        """主要的抓取方法"""
        # 获取页面内容
        html_content = self.fetch_page(self.target_url)
        if not html_content:
            return []

        # 解析新闻内容
        news_items = self.parse_news_content(html_content)
        return news_items


def main():
    """主函数"""
    scraper = CFSHKScraper()

    # 抓取新闻信息
    news_items = scraper.scrape_news()

    # 输出所有新闻项目
    for item in news_items:
        # 从内容中提取链接
        content = item['内容']
        link = ""
        title = content

        # 提取链接信息
        if " [链接: " in content:
            parts = content.split(" [链接: ")
            title = parts[0].strip()
            if len(parts) > 1:
                link = parts[1].rstrip("]")

        print(f"任务标题: {title}")
        print(f"任务摘要: ")
        print(f"食品类别: ")
        print(f"风险类别: ")
        print(f"风险因子: ")
        print(f"发布日期: {item['日期']}")
        print(f"原始链接: {link}")
        print(f"信息通报国别: 中国香港")
        print(f"产地国别: 中国香港")
        print("-" * 50)  # 分隔线


if __name__ == "__main__":
    main()