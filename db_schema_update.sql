-- 通用数据表
CREATE TABLE capture_data_general (
    id              NUMBER(20)      PRIMARY KEY,
    batch_id        VARCHAR2(50)    NOT NULL,
    task_id         NUMBER(10)      NOT NULL,
    title           VARCHAR2(500),
    summary         VARCHAR2(4000),
    publish_date    VARCHAR2(50),
    category        VARCHAR2(200),
    risk_category   VARCHAR2(200),  -- 风险类别
    risk_factor     VARCHAR2(200),
    source_url      VARCHAR2(1000),
    country         VARCHAR2(100),
    origin_country  VARCHAR2(100),
    created_at      DATE            DEFAULT SYSDATE
);

COMMENT ON COLUMN capture_data_general.id IS '主键，数据唯一ID';
COMMENT ON COLUMN capture_data_general.batch_id IS '关联批次ID';
COMMENT ON COLUMN capture_data_general.task_id IS '关联任务配置ID';
COMMENT ON COLUMN capture_data_general.title IS '任务标题';
COMMENT ON COLUMN capture_data_general.summary IS '任务摘要';
COMMENT ON COLUMN capture_data_general.publish_date IS '发布日期';
COMMENT ON COLUMN capture_data_general.category IS '食品类别';
COMMENT ON COLUMN capture_data_general.risk_category IS '风险类别';
COMMENT ON COLUMN capture_data_general.risk_factor IS '风险因子';
COMMENT ON COLUMN capture_data_general.source_url IS '原始链接';
COMMENT ON COLUMN capture_data_general.country IS '通报国别';
COMMENT ON COLUMN capture_data_general.origin_country IS '产地国别';
COMMENT ON COLUMN capture_data_general.created_at IS '记录创建时间';