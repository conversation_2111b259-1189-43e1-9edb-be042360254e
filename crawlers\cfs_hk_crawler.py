import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import urllib3
from datetime import datetime, timedelta
from crawlers.base_crawler import BaseCrawler
from models.data_general import DataGeneral

class CFSHKCrawler(BaseCrawler):
    """
    香港食物安全中心爬虫
    抓取 https://www.cfs.gov.hk/sc_chi/whatsnew/whatsnew_fstr/whatsnew_fstr.html 页面的内容
    """
    
    def __init__(self, task_id, batch_id=None):
        """
        初始化爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
        """
        super().__init__(task_id, batch_id)
        self.base_url = "https://www.cfs.gov.hk"
        self.target_url = "https://www.cfs.gov.hk/sc_chi/whatsnew/whatsnew_fstr/whatsnew_fstr.html"
        self.source = "香港食物安全中心"
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 配置会话以跳过SSL验证
        self.session.verify = False
        
        # 更新会话头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def convert_date_format(self, date_str):
        """
        将日期从 'dd.mm.yyyy' 格式转换为 'yyyy-mm-dd' 格式
        
        Args:
            date_str (str): 原始日期字符串
            
        Returns:
            str: 转换后的日期字符串
        """
        try:
            # 解析原始日期格式 dd.mm.yyyy
            date_obj = datetime.strptime(date_str, '%d.%m.%Y')
            # 转换为目标格式 yyyy-mm-dd
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            # 如果解析失败，返回原始字符串
            return date_str
    
    def is_within_last_month(self, date_str):
        """
        检查日期是否在最近一个月内
        
        Args:
            date_str (str): 日期字符串，格式为 'dd.mm.yyyy'
            
        Returns:
            bool: 如果日期在最近一个月内，返回True，否则返回False
        """
        try:
            # 解析日期
            date_obj = datetime.strptime(date_str, '%d.%m.%Y')
            # 获取当前日期
            current_date = datetime.now()
            # 计算一个月前的日期
            one_month_ago = current_date - timedelta(days=30)
            # 检查是否在范围内
            return date_obj >= one_month_ago and date_obj <= current_date
        except ValueError:
            # 如果解析失败，默认保留
            return True
    
    def parse_news_content(self, html_content):
        """
        解析新闻内容
        
        Args:
            html_content (str): HTML内容
            
        Returns:
            list: 新闻项目列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []
        
        # 查找所有tr元素
        table_rows = soup.find_all('tr')
        
        for tr in table_rows:
            # 查找包含日期的td.subHeader
            date_td = tr.find('td', class_='subHeader')
            if date_td:
                # 提取日期文本，去掉箭头符号
                date_text = date_td.get_text().strip()
                # 移除箭头符号和其他特殊字符
                date_text = date_text.replace('▼', '').replace('▲', '').replace('►', '').replace('◄', '').replace('&nbsp;', '').strip()
                
                # 检查日期是否在最近一个月内
                if not self.is_within_last_month(date_text):
                    continue
                
                # 转换日期格式
                formatted_date = self.convert_date_format(date_text)
                
                # 查找同一行中的其他td，包含ul和li
                content_td = tr.find('td', class_=lambda x: x != 'subHeader')
                if content_td:
                    # 查找所有li元素
                    li_elements = content_td.find_all('li')
                    
                    if li_elements:
                        # 为每个li创建一个独立的新闻项目
                        for li in li_elements:
                            # 获取li的文本内容
                            li_text = li.get_text().strip()
                            # 如果li中有链接，也提取链接信息
                            link = li.find('a')
                            source_url = ""
                            if link:
                                href = link.get('href', '')
                                if href:
                                    source_url = urljoin(self.base_url, href)
                            
                            # 为每个li创建一个独立的新闻项目
                            news_item = {
                                'date': formatted_date,
                                'content': li_text,
                                'source_url': source_url
                            }
                            news_items.append(news_item)
        
        return news_items
    
    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            self.logger.info("开始抓取香港食物安全中心数据")
            
            # 获取页面内容
            response = self.make_request(self.target_url, timeout=30, verify=False)
            if not response:
                self.logger.error("请求页面失败")
                return False
            
            # 设置响应编码
            response.encoding = 'utf-8'
            html_content = response.text
            
            # 解析新闻内容
            news_items = self.parse_news_content(html_content)
            if not news_items:
                self.logger.warning("未抓取到任何香港食物安全中心数据")
                return False
            
            self.logger.info(f"解析到 {len(news_items)} 条新闻")
            
            # 转换为数据记录格式
            data_records = []
            for item in news_items:
                # 从内容中提取标题和链接
                content = item['content']
                title = content
                
                data_record = {
                    'title': title,
                    'summary': "",
                    'publish_date': item['date'],
                    'category': "",
                    'risk_category': "",
                    'risk_factor': "",
                    'source_url': item['source_url'],
                    'country': "中国香港",
                    'origin_country': "中国香港"
                }
                data_records.append(data_record)
            
            # 保存数据
            if data_records:
                self.save_records(data_records, DataGeneral)
                self.logger.info(f"成功保存 {len(data_records)} 条香港食物安全中心数据")
                return True
            else:
                self.logger.warning("未能保存任何香港食物安全中心数据")
                return False
                
        except Exception as e:
            self.logger.error(f"抓取香港食物安全中心数据失败: {e}")
            return False
