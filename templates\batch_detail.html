{% extends 'base.html' %}

{% block title %}批次详情 - 数据抓取管理平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in">
    <div>
        <h2 class="mb-1">批次详情</h2>
        <p class="text-muted">查看批次信息和数据记录</p>
    </div>
    <a href="{{ url_for('batches.batch_list') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-1"></i> 返回批次列表
    </a>
</div>

<div class="card mb-4 fade-in">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">批次信息</h5>
        <span class="badge {% if batch.status == 'SUCCESS' %}bg-success{% elif batch.status == 'FAILED' %}bg-danger{% elif batch.status == 'RUNNING' %}bg-warning text-dark{% else %}bg-secondary{% endif %}">
            {% if batch.status == 'SUCCESS' %}
            <i class="bi bi-check-circle me-1"></i> 成功
            {% elif batch.status == 'FAILED' %}
            <i class="bi bi-x-circle me-1"></i> 失败
            {% elif batch.status == 'RUNNING' %}
            <i class="bi bi-hourglass-split me-1"></i> 运行中
            {% else %}
            <i class="bi bi-question-circle me-1"></i> {{ batch.status }}
            {% endif %}
        </span>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span class="text-muted">批次ID</span>
                        <span class="fw-bold">{{ batch.batch_id }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span class="text-muted">任务名称</span>
                        <span>
                            <i class="bi bi-list-task text-primary me-1"></i>
                            {{ task.task_name if task else '未知任务' }}
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span class="text-muted">抓取时间</span>
                        <span>
                            <i class="bi bi-calendar-event me-1"></i>
                            {{ batch.capture_time|datetime }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span class="text-muted">记录数量</span>
                        <span class="badge bg-light text-dark">{{ batch.record_count }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span class="text-muted">数据类型</span>
                        <span class="badge {% if task_type == 'fda' %}bg-info{% elif task_type == 'rybzszh' %}bg-primary{% elif task_type == 'wanfang_magazine' %}bg-success{% elif task_type == 'general' %}bg-warning text-dark{% else %}bg-secondary{% endif %} text-white">
                            {% if task_type == 'fda' %}
                            FDA数据
                            {% elif task_type == 'rybzszh' %}
                            乳业标准数字化
                            {% elif task_type == 'wanfang_magazine' %}
                            万方期刊数据
                            {% elif task_type == 'general' %}
                            通用数据
                            {% else %}
                            未知类型
                            {% endif %}
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span class="text-muted">操作</span>
                        <div class="btn-group">
                            <a href="{{ url_for('batches.export_batch', batch_id=batch.batch_id, task_type=task_type) }}" class="btn btn-sm btn-outline-success" title="导出数据">
                                <i class="bi bi-file-earmark-excel me-1"></i> 导出
                            </a>
                            <a href="{{ url_for('batches.batch_delete', batch_id=batch.batch_id) }}" class="btn btn-sm btn-outline-danger" 
                               onclick="return confirm('确定要删除此批次吗？')" title="删除批次">
                                <i class="bi bi-trash me-1"></i> 删除
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card fade-in">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">数据记录</h5>
        <span class="badge bg-light text-dark">共 {{ batch.record_count }} 条记录</span>
    </div>
    <div class="card-body">
        {# 根据任务类型加载不同的数据表格模板 #}
        {% if task_type == 'fda' %}
            {% include 'batch_details/fda_table.html' %}
        {% elif task_type == 'rybzszh' %}
            {% include 'batch_details/rybzszh_table.html' %}
        {% elif task_type == 'wanfang_magazine' %}
            {% include 'batch_details/wanfang_magazine_table.html' %}
        {% elif task_type == 'general' %}
            {% include 'batch_details/general_table.html' %}
        {% else %}
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                未知的数据类型: {{ task_type }}
            </div>
        {% endif %}
        
        {% if total_pages > 1 %}
        <nav aria-label="记录分页" class="mt-4">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if page == 1 %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id, page=page-1) }}" aria-label="上一页">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                
                {% for p in range(1, total_pages + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id, page=p) }}">{{ p }}</a>
                </li>
                {% endfor %}
                
                <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id, page=page+1) }}" aria-label="下一页">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- 详情弹窗 -->
<div class="modal fade" id="recordDetailModal" tabindex="-1" aria-labelledby="recordDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recordDetailModalLabel">记录详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-5" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载数据...</p>
                </div>
                <div id="recordDetailContent" style="display: none;">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a href="#" class="btn btn-primary" id="sourceUrlLink" target="_blank">
                    <i class="bi bi-link-45deg"></i> 访问原始链接
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/batch_detail.js') }}"></script>
{% endblock %}