{% extends 'base.html' %}

{% block title %}批次管理 - 数据抓取管理平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in">
    <div>
        <h2 class="mb-1">批次管理</h2>
        <p class="text-muted">管理和查看数据抓取批次</p>
    </div>
    <div>
        <form method="get" class="d-flex gap-2">
            <select name="task_id" class="form-select">
                <option value="">所有任务</option>
                {% for task in tasks %}
                <option value="{{ task.id }}" {% if selected_task_id == task.id %}selected{% endif %}>{{ task.task_name }}</option>
                {% endfor %}
            </select>
            <button type="submit" class="btn btn-primary" style="width: 120px">
                <i class="bi bi-filter me-1"></i> 筛选
            </button>
        </form>
    </div>
</div>

<div class="card fade-in">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover align-middle data-table" id="batchTable">
                <thead>
                    <tr>
                        <th>批次ID</th>
                        <th>任务名称</th>
                        <th>抓取时间</th>
                        <th>记录数</th>
                        <th>状态</th>
                        <th>备注</th>
                        <th style="width: 150px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for batch in batches %}
                    <tr>
                        <td>
                            <a href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id) }}" class="text-decoration-none fw-bold">
                                {{ batch.batch_id }}
                            </a>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-collection text-primary me-2"></i>
                                {{ task_names.get(batch.task_id, '未知任务') }}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-calendar-event text-secondary me-2"></i>
                                {{ batch.capture_time.strftime('%Y-%m-%d %H:%M:%S') if batch.capture_time else '' }}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">{{ batch.record_count }}</span>
                        </td>
                        <td>
                            {% if batch.status == 'SUCCESS' %}
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle me-1"></i> 成功
                            </span>
                            {% elif batch.status == 'FAILED' %}
                            <span class="badge bg-danger">
                                <i class="bi bi-x-circle me-1"></i> 失败
                            </span>
                            {% elif batch.status == 'RUNNING' %}
                            <span class="badge bg-warning text-dark">
                                <i class="bi bi-hourglass-split me-1"></i> 运行中
                            </span>
                            {% else %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-question-circle me-1"></i> {{ batch.status }}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if batch.remark %}
                            <span data-bs-toggle="tooltip" title="{{ batch.remark }}">
                                {{ batch.remark|truncate(20) }}
                            </span>
                            {% else %}
                            <span class="text-muted">无</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id) }}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('batches.batch_delete', batch_id=batch.batch_id) }}" class="btn btn-sm btn-outline-danger" 
                                   onclick="return confirm('确定要删除此批次吗？')" data-bs-toggle="tooltip" title="删除批次">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-inbox fs-2 d-block mb-2"></i>
                                暂无批次数据
                            </div>
                            {% if selected_task_id %}
                            <a href="{{ url_for('batches.batch_list') }}" class="btn btn-sm btn-primary mt-2">
                                <i class="bi bi-funnel me-1"></i> 清除筛选
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- 移除自定义分页，使用DataTables自带分页 -->
    </div>
</div>

<div class="card mt-4 fade-in">
    <div class="card-header">
        <h5 class="mb-0">批次状态说明</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-group">
                    <li class="list-group-item d-flex align-items-center">
                        <span class="badge bg-success me-2">
                            <i class="bi bi-check-circle"></i>
                        </span>
                        <strong>成功</strong> - 批次已完成，数据抓取成功
                    </li>
                    <li class="list-group-item d-flex align-items-center">
                        <span class="badge bg-danger me-2">
                            <i class="bi bi-x-circle"></i>
                        </span>
                        <strong>失败</strong> - 批次执行失败，可能需要检查错误日志
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-group">
                    <li class="list-group-item d-flex align-items-center">
                        <span class="badge bg-warning text-dark me-2">
                            <i class="bi bi-hourglass-split"></i>
                        </span>
                        <strong>运行中</strong> - 批次正在执行中
                    </li>
                    <li class="list-group-item d-flex align-items-center">
                        <span class="badge bg-secondary me-2">
                            <i class="bi bi-question-circle"></i>
                        </span>
                        <strong>其他状态</strong> - 系统定义的其他状态
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化提示工具
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // 为批次表格配置DataTables，确保按时间降序排列
        if ($.fn.DataTable.isDataTable('#batchTable')) {
            // 如果表格已经被初始化为DataTable，则销毁它
            $('#batchTable').DataTable().destroy();
        }
        
        $('#batchTable').DataTable({
            language: {
                url: '/static/i18n/zh.json'
            },
            responsive: true,
            paging: true,   // 启用DataTables的分页
            searching: true,
            info: true,     // 启用显示"显示第 1 至 10 项结果，共 X 项"的信息
            pageLength: 10, // 每页显示10条记录
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]],
            order: [[2, 'desc']], // 按第3列（抓取时间）降序排序
            columnDefs: [
                { orderable: false, targets: 6 } // 禁用最后一列（操作列）的排序
            ]
        });
    });
</script>
{% endblock %} 