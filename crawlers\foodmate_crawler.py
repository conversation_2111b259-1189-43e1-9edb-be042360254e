import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import time
import re
import logging
import urllib3
from crawlers.base_crawler import BaseCrawler
from models.data_general import DataGeneral

class FoodmateCrawler(BaseCrawler):
    """
    食品伙伴网标准爬虫
    """
    
    def __init__(self, task_id, batch_id=None):
        """
        初始化爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
        """
        super().__init__(task_id, batch_id)
        self.base_url = "https://tag.foodmate.net/1647/standard/"
        self.source = "食品伙伴网"
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 创建适配器和重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]  # 在新版本中method_whitelist已更名为allowed_methods
        )
        
        # 创建适配器并挂载到会话
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
    def _get_standard_details(self, detail_url):
        """
        获取标准详细信息
        
        Args:
            detail_url (str): 详情页URL
            
        Returns:
            tuple: (发布日期, 内容)
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 直接使用session而不是make_request，以便更好地控制异常处理
                self.logger.info(f"获取标准详情: {detail_url}")
                
                # 使用更长的超时时间
                response = self.session.get(detail_url, timeout=30)
                response.raise_for_status()
                
                # 检查响应内容是否为空
                if not response.content:
                    self.logger.warning(f"响应内容为空: {detail_url}")
                    retry_count += 1
                    time.sleep(2)
                    continue
                
                # 解析HTML
                soup = BeautifulSoup(response.content, "html.parser")
                
                # 获取发布日期
                publish_date = None
                tables = soup.find_all("table")
                for table in tables:
                    rows = table.find_all("tr")
                    for row in rows:
                        cells = row.find_all(["th", "td"])
                        for i, cell in enumerate(cells):
                            if "发布日期" in cell.get_text(strip=True) and i + 1 < len(cells):
                                publish_date = cells[i + 1].get_text(strip=True)
                                break
                        if publish_date:
                            break
                    if publish_date:
                        break
                
                # 获取适用范围内容
                content = None
                bznr_box = soup.find("div", class_="bznr_box")
                if bznr_box:
                    # 查找适用范围相关的div
                    divs = bznr_box.find_all("div")
                    for div in divs:
                        text = div.get_text(strip=True)
                        if "本文件规定了" in text or "适用于" in text:
                            content = text
                            break
                
                return publish_date, content
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {retry_count+1}/{max_retries}): {detail_url}, 错误: {e}")
                retry_count += 1
                time.sleep(2)
            except Exception as e:
                self.logger.error(f"获取标准详情失败: {e}")
                return None, None
        
        self.logger.error(f"获取标准详情失败，已达到最大重试次数: {detail_url}")
        return None, None
    
    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            self.logger.info("开始抓取食品伙伴网标准数据")
            
            # 时间范围：今天 ~ 一个月前
            today = datetime.today().date()
            one_month_ago = today - timedelta(days=30)
            
            page = 1
            stop_flag = False
            all_results = []
            max_retries = 3
            
            while True:
                if page == 1:
                    url = self.base_url
                else:
                    url = f"{self.base_url}index-{page}.html"
                
                self.logger.info(f"正在抓取第 {page} 页: {url}")
                
                # 使用重试机制获取列表页
                retry_count = 0
                response = None
                
                while retry_count < max_retries:
                    try:
                        # 直接使用session而不是make_request
                        response = self.session.get(url, timeout=30)
                        response.raise_for_status()
                        break  # 成功获取响应，跳出重试循环
                    except requests.RequestException as e:
                        self.logger.warning(f"请求页面失败 (尝试 {retry_count+1}/{max_retries}): {url}, 错误: {e}")
                        retry_count += 1
                        time.sleep(2)
                
                if not response:
                    self.logger.error(f"请求页面失败，已达到最大重试次数: {url}")
                    break
                
                # 使用content而不是text，避免编码问题
                soup = BeautifulSoup(response.content, "html.parser")
                
                titles = soup.find_all("li", class_="list_bt")
                dates = soup.find_all("ul", class_="list_wzr")
                
                if not titles or not dates:
                    self.logger.info("没有找到更多数据，结束抓取")
                    break
                
                for title_li, date_ul in zip(titles, dates):
                    # 获取标题和链接
                    title_link = title_li.find("a")
                    if not title_link:
                        continue
                    
                    title = title_link.get_text(strip=True)
                    detail_url = title_link.get("href")
                    
                    # 如果是相对链接，转换为绝对链接
                    if detail_url and not detail_url.startswith("http"):
                        detail_url = "http://down.foodmate.net" + detail_url
                    
                    date_str = date_ul.get_text(strip=True)
                    
                    try:
                        list_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                    except ValueError:
                        continue
                    
                    if list_date < one_month_ago:
                        stop_flag = True
                        break  # 当前页已经到一个月前，停止采集
                    elif list_date <= today:
                        # 获取详细信息
                        publish_date, content = self._get_standard_details(detail_url)
                        
                        all_results.append({
                            'title': title,
                            'summary': content or "",
                            'publish_date': publish_date or date_str,
                            'category': "",
                            'risk_category': "",
                            'risk_factor': "",
                            'source': self.source,
                            'source_url': detail_url,
                            'country': "中国",
                            'origin_country': "中国"
                        })
                        
                        time.sleep(1)  # 增加间隔时间，避免请求过快
                
                if stop_flag or len(all_results) >= 100:  # 限制最大抓取数量
                    break
                
                page += 1
                time.sleep(2)  # 增加间隔时间，防止过快请求被封
            
            # 保存结果
            if all_results:
                self.save_records(all_results, DataGeneral)
                self.logger.info(f"成功抓取 {len(all_results)} 条食品伙伴网标准数据")
                return True
            else:
                self.logger.warning("未抓取到任何食品伙伴网标准数据")
                return False
                
        except Exception as e:
            self.logger.error(f"抓取食品伙伴网标准数据失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())  # 打印完整的异常堆栈
            return False
