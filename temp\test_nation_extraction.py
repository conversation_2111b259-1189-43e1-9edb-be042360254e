#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试nation标签提取功能
"""

def extract_nation_labels(nation_data):
    """从nation数据中提取label字段
    
    Args:
        nation_data: 可能是列表格式，如 [{id: "33", label: "Northern Ireland"}]
        
    Returns:
        list: 提取的国家标签列表
    """
    nations = []
    
    if isinstance(nation_data, list):
        for nation_item in nation_data:
            if isinstance(nation_item, dict) and 'label' in nation_item:
                nations.append(nation_item['label'])
    elif isinstance(nation_data, dict) and 'label' in nation_data:
        # 如果是单个字典对象
        nations.append(nation_data['label'])
        
    return nations


def test_nation_extraction():
    """测试nation标签提取功能"""
    
    print("=== Nation标签提取测试 ===")
    
    # 测试1: 列表格式的nation数据
    test_data_1 = [
        {"id": "33", "label": "Northern Ireland"},
        {"id": "34", "label": "Scotland"},
        {"id": "35", "label": "Wales"}
    ]
    
    result_1 = extract_nation_labels(test_data_1)
    print(f"测试1 - 列表格式:")
    print(f"输入: {test_data_1}")
    print(f"提取的国家标签: {result_1}")
    print()
    
    # 测试2: 单个字典格式
    test_data_2 = {"id": "33", "label": "Northern Ireland"}
    
    result_2 = extract_nation_labels(test_data_2)
    print(f"测试2 - 单个字典格式:")
    print(f"输入: {test_data_2}")
    print(f"提取的国家标签: {result_2}")
    print()
    
    # 测试3: 空列表
    test_data_3 = []
    
    result_3 = extract_nation_labels(test_data_3)
    print(f"测试3 - 空列表:")
    print(f"输入: {test_data_3}")
    print(f"提取的国家标签: {result_3}")
    print()
    
    # 测试4: 包含无效数据的列表
    test_data_4 = [
        {"id": "33", "label": "Northern Ireland"},
        {"id": "34"},  # 缺少label字段
        {"label": "Scotland"},  # 缺少id字段，但有label
        "invalid_data",  # 非字典数据
        {"id": "35", "label": "Wales"}
    ]
    
    result_4 = extract_nation_labels(test_data_4)
    print(f"测试4 - 包含无效数据的列表:")
    print(f"输入: {test_data_4}")
    print(f"提取的国家标签: {result_4}")
    print()
    
    # 测试5: None值
    test_data_5 = None
    
    result_5 = extract_nation_labels(test_data_5)
    print(f"测试5 - None值:")
    print(f"输入: {test_data_5}")
    print(f"提取的国家标签: {result_5}")
    print()


if __name__ == "__main__":
    test_nation_extraction()
